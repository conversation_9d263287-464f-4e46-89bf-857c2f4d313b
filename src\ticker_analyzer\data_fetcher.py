import robin_stocks.robinhood as r
import pandas as pd
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timezone
from tabulate import tabulate
import yfinance as yf
import os
from dotenv import load_dotenv
from common.http_session import get_session
import asyncio
from functools import partial
import aiohttp

# Disable SSL verification to fix certificate issues
session = get_session('src/certs/my_ca_bundle.pem')

# Load environment variables from .env file
load_dotenv()

# Get Robinhood credentials from environment variables
rh_un = os.environ.get('RH_UN')
rh_pw = os.environ.get('RH_PW')

# Check if credentials are available
if not rh_un or not rh_pw:
    print("Warning: Robinhood credentials not found in environment variables. Some features may be limited.")

# Import from sibling modules within the package
from .utils import str_to_num, cache_for_one_hour, format_market_cap

# Import from potentially external utilities/packages relative to src
try:
    from util.scrapper import fundHoldingsYahoo, etfHoldingsETFDBdotCOM, CNNFearGreed, squeezeMetrics
    from stockdata.factors import calculateFactors
except ImportError as e:
    print(f"Warning: Could not import external utilities (scrapper, factors) using direct paths: {e}")
    def fundHoldingsYahoo(session, ticker): return pd.DataFrame()
    def etfHoldingsETFDBdotCOM(session, ticker): return pd.DataFrame()
    def CNNFearGreed(session): return 0
    def squeezeMetrics(session): return pd.DataFrame()
    def calculateFactors(): return 0.0

async def _run_sync_in_executor(func, *args, **kwargs):
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))

async def _fallback_to_yfinance_eod(symbol):
    """
    Fallback function to get EOD data from yfinance when Robinhood intraday data fails.
    Converts yfinance format to Robinhood-compatible format.

    Note: This provides EOD data only, not the 5-minute intraday data that RH provides.
    """
    try:
        from stockdata.data_source import getStockDataV3

        print(f"Fetching EOD data from yfinance for {symbol}...")
        async with aiohttp.ClientSession() as fallback_session:
            yf_data = await getStockDataV3(fallback_session, symbol)

            if yf_data is None or yf_data.empty:
                print("yfinance EOD fallback also failed.")
                return pd.DataFrame()

            # Convert yfinance format to Robinhood format
            # yfinance: Open, High, Low, Close, Volume + technical indicators
            # Robinhood: open_price, high_price, low_price, close_price, volume + begins_at index

            rh_format_data = pd.DataFrame()
            rh_format_data['open_price'] = yf_data['Open']
            rh_format_data['high_price'] = yf_data['High']
            rh_format_data['low_price'] = yf_data['Low']
            rh_format_data['close_price'] = yf_data['Close']
            rh_format_data['volume'] = yf_data['Volume']

            # Set the index name to 'begins_at' for Robinhood compatibility
            rh_format_data.index = yf_data.index
            rh_format_data.index.name = 'begins_at'

            # Convert to proper data types
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                rh_format_data[col] = pd.to_numeric(rh_format_data[col], errors='coerce')

            print(f"Successfully converted yfinance EOD data to Robinhood format.")
            print(f"Note: Using EOD data only - no intraday 5-minute data available.")
            print(f"Latest data point: Index={rh_format_data.index[-1]}, Close={rh_format_data['close_price'].iloc[-1]:.2f}, Volume={rh_format_data['volume'].iloc[-1]:,.0f}")

            return rh_format_data

    except Exception as e:
        print(f"Error in yfinance EOD fallback: {e}")
        return pd.DataFrame()

async def login_robinhood():
    """Logs into Robinhood asynchronously."""
    if rh_un and rh_pw:
        try:
            await _run_sync_in_executor(r.authentication.login, username=rh_un, password=rh_pw)
            print("Robinhood login successful.")
            return True
        except Exception as e:
            print(f"Robinhood login failed: {e}")
            return False
    else:
        print("Robinhood credentials not found. Skipping login.")
        return False

# Apply cache decorator
@cache_for_one_hour
async def get_cached_factors(session: aiohttp.ClientSession):
    """Calculates and caches factors asynchronously."""
    try:
        print("Calculating risk factors...")
        factors = await calculateFactors(session)
        print(f"Raw factors value: {factors}")
        return round(factors, 2)
    except Exception as e:
        import traceback
        print(f"Error calculating factors: {e}")
        traceback.print_exc()
        return 0.0


@cache_for_one_hour
async def fetch_stock_data(symbol):
    """Fetches historical stock data, combining Robinhood and web scraping asynchronously."""
    print(f"Fetching data for {symbol}...")
    data = pd.DataFrame()
    data1 = pd.DataFrame()
    df_day = pd.DataFrame()

    # Fetch daily data from Robinhood
    try:
        data = await _run_sync_in_executor(r.stocks.get_stock_historicals, symbol, interval='day', span='year')
        data = pd.DataFrame(data)
        if data.empty:
            print("Warning: Robinhood daily historicals returned empty data.")
    except Exception as e:
        print(f"Error fetching Robinhood daily historicals: {e}")

    # Fetch 5-minute data for today from Robinhood
    try:
        data1 = await _run_sync_in_executor(r.stocks.get_stock_historicals, symbol, interval='5minute', span='day', bounds="extended")
        data1 = pd.DataFrame(data1)
        if data1.empty:
            print("Warning: Robinhood 5-min historicals returned empty data.")
    except Exception as e:
        print(f"Error fetching Robinhood 5-min historicals: {e}")

    # Process 5-minute data to get today's OHLCV
    try:
        if not data1.empty:
            data1['begins_at'] = pd.to_datetime(data1['begins_at'])
            data1['date'] = data1['begins_at'].dt.date

            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                data1[col] = pd.to_numeric(data1[col], errors='coerce')

            df_day = data1.groupby('date').agg(
                begins_at=('begins_at', 'last'),
                open_price=('open_price', 'first'),
                close_price=('close_price', 'last'),
                high_price=('high_price', 'max'),
                low_price=('low_price', 'min'),
                volume=('volume', 'sum')
            )

            # Attempt to scrape current volume from Robinhood website asynchronously
            try:
                url = f'https://robinhood.com/stocks/{symbol}/'
                async with aiohttp.ClientSession() as session_aio:
                    async with session_aio.get(url, timeout=10) as response_aio:
                        response_aio.raise_for_status()
                        content = await response_aio.text()
                        soup = BeautifulSoup(content, 'html.parser')
                        volume_span = soup.find('span', text='Volume')
                        if volume_span:
                            volume_data = volume_span.find_next_sibling('span').text.strip()
                            if not df_day.empty:
                                df_day.loc[df_day.index[-1], 'volume'] = str_to_num(volume_data)
            except Exception as e:
                print(f"Error scraping volume from Robinhood: {e}")

    except Exception as e:
        print(f"Error processing 5-minute data: {e}")
        df_day = pd.DataFrame()

    # Check if we have any Robinhood data at all, if not, use yfinance fallback immediately
    if data.empty and df_day.empty:
        print("No Robinhood intraday data available. Using yfinance EOD fallback...")
        return await _fallback_to_yfinance_eod(symbol)

    # Combine historical and today's data
    try:
        if not data.empty:
            data['begins_at'] = pd.to_datetime(data['begins_at'])
            required_cols = ['begins_at', 'open_price', 'close_price', 'high_price', 'low_price', 'volume']
            for col in required_cols:
                if col not in data.columns:
                    data[col] = pd.NA
                if col != 'begins_at':
                    data[col] = pd.to_numeric(data[col], errors='coerce')

            if not df_day.empty:
                latest_date_in_df_day = df_day.index[-1]
                df_day.reset_index(inplace=True)
                df_day = df_day.reindex(columns=data.columns)
                data = data[data['begins_at'].dt.date != latest_date_in_df_day]
                data = pd.concat([data, df_day], ignore_index=True)

            data.set_index('begins_at', inplace=True)
            data.sort_index(inplace=True)

            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in data.columns:
                    data[col] = data[col].astype(float)

            print(f"Latest data point: Index={data.index[-1]}, Close={data['close_price'].iloc[-1]:.2f}, Volume={data['volume'].iloc[-1]:,.0f}")
            return data

        elif not df_day.empty:
            print("Warning: Historical data is empty, using only today's data.")
            df_day.set_index('begins_at', inplace=True)
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in df_day.columns:
                    df_day[col] = df_day[col].astype(float)
            return df_day

        else:
            print("Error: Both historical and today's data fetching failed.")
            print("Attempting to use yfinance EOD fallback...")
            return await _fallback_to_yfinance_eod(symbol)

    except Exception as e:
        print(f"Error combining or processing data: {e}")
        print("Attempting to use yfinance EOD fallback...")
        return await _fallback_to_yfinance_eod(symbol)


async def fetch_basic_info_and_news(symbol):
    """Fetches basic company info and news using yfinance asynchronously."""
    print("\n--- Ticker Information & News ---")
    try:
        tickerData = await _run_sync_in_executor(yf.Ticker, symbol, session=session)
        info = await _run_sync_in_executor(lambda: tickerData.info)

        description = info.get('longBusinessSummary', 'N/A')
        sector = info.get('sector', 'N/A')
        industry = info.get('industry', 'N/A')
        market_cap_raw = info.get('marketCap', 0)
        market_cap = format_market_cap(market_cap_raw)

        earnings_dates = await _run_sync_in_executor(lambda: tickerData.calendar) if hasattr(tickerData, 'calendar') else None
        earnings_date = 'N/A'
        if earnings_dates is not None and isinstance(earnings_dates, pd.DataFrame) and not earnings_dates.empty:
            try:
                raw_date = earnings_dates.iloc[0, earnings_dates.columns.get_loc('Earnings Date')]
                if pd.notna(raw_date):
                    earnings_date = pd.to_datetime(raw_date).strftime('%Y-%m-%d')
            except Exception as e:
                print(f"Could not parse earnings date: {e}")
                earnings_date = 'N/A'

        print(f"Description: {description[:150]}...")
        print(f"Sector: {sector}")
        print(f"Industry: {industry}")
        print(f"Earnings Date: {earnings_date}")
        print(f"Market Cap: {market_cap}")

        print("\n--- Recent News (Yahoo) ---")
        try:
            news = await _run_sync_in_executor(tickerData.get_news, count=5, tab="news")
            if news and isinstance(news, list) and len(news) > 0:
                for item in news[:5]:
                    title = item.get('title', 'N/A')
                    if 'content' in item and 'title' in item['content']:
                        title = item['content']['title']

                    link = '#'
                    if 'content' in item and 'clickThroughUrl' in item['content']:
                        if 'url' in item['content']['clickThroughUrl']:
                            link = item['content']['clickThroughUrl']['url']
                    elif 'link' in item:
                        link = item['link']

                    print(f"- {title} ({link})")
            else:
                print("No recent news found via yfinance.")
        except Exception as news_error:
            print(f"Error fetching news: {news_error}")
            print("No recent news found via yfinance.")

    except Exception as e:
        print(f"Error fetching basic info/news from yfinance: {e}")


async def print_catalyst_news(symbol):
    """Fetches and prints catalyst news from Robinhood asynchronously."""
    print("\n--- Catalyst News (Robinhood) ---")
    try:
        today_utc = datetime.now(timezone.utc).date().isoformat()
        today_local = datetime.now().date().isoformat()

        all_news = await _run_sync_in_executor(r.stocks.get_news, symbol)

        if not all_news:
            print("No news found from Robinhood.")
            return

        todays_news = [news for news in all_news if
                      news.get('published_at', '').startswith(today_local) or
                      news.get('published_at', '').startswith(today_utc)]

        if todays_news:
            for news in todays_news[:5]:
                title = news.get('title', 'N/A')
                print(f"- {title}")
        else:
            print("No news published today found from Robinhood.")
            print("\n--- Recent Headlines ---")
            for news in all_news[:3]:
                title = news.get('title', 'N/A')
                print(f"- {title}")

    except Exception as e:
        print(f"Error fetching Robinhood news: {e}")
        import traceback
        traceback.print_exc()


def fetch_and_print_highs_lows(data):
    """Calculates and prints historical highs/lows relative to the current price."""
    print("\n--- Highs & Lows ---")
    if data.empty or 'close_price' not in data.columns:
        print("Insufficient data for highs/lows calculation.")
        return

    try:
        # Ensure index is datetime
        if not isinstance(data.index, pd.DatetimeIndex):
             data.index = pd.to_datetime(data.index)

        # Calculate the highs and lows for each timeframe
        timeframes = {'1w': '7D', '1m': '30D', '3m': '90D', '6m': '180D', '12m': '365D'} # Use pandas offset strings
        results = {}
        current_price = data['close_price'].iloc[-1]

        for tf, duration in timeframes.items():
            # Rolling window calculation - ensure enough data points
            if len(data) > 1:
                 # Shift(1) to get the high/low *before* the current day
                 high_prices = data['close_price'].rolling(window=duration, min_periods=1).max().shift(1)
                 low_prices = data['close_price'].rolling(window=duration, min_periods=1).min().shift(1)
                 prev_high = high_prices.iloc[-1] if not high_prices.empty else pd.NA
                 prev_low = low_prices.iloc[-1] if not low_prices.empty else pd.NA
            else:
                 prev_high = pd.NA
                 prev_low = pd.NA


            results[tf] = {
                'high': prev_high if pd.notna(prev_high) else 'N/A',
                'low': prev_low if pd.notna(prev_low) else 'N/A',
                # Compare current price to the *previous* period's high/low
                'is_high': current_price >= prev_high if pd.notna(prev_high) else False,
                'is_low': current_price <= prev_low if pd.notna(prev_low) else False
            }

        # Create a DataFrame from the dictionary
        df = pd.DataFrame(results).T

        # Rename columns
        df.columns = ["Prev High", "Prev Low", ">= Prev High", "<= Prev Low"]

        # Format numbers and boolean strings
        df["Prev High"] = df["Prev High"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
        df["Prev Low"] = df["Prev Low"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
        df.replace({True: 'True', False: 'False'}, inplace=True)


        # Print the DataFrame
        print(tabulate(df, headers='keys', tablefmt='fancy_grid'))

    except Exception as e:
        print(f"Error calculating highs/lows: {e}")


async def fetch_etf_holdings(session: aiohttp.ClientSession, symbol):
    """Fetches ETF holdings asynchronously."""
    print("\n--- ETF Holdings (ETFDB.com) ---")
    try:
        etf_holdings = await etfHoldingsETFDBdotCOM(session, symbol)
        if not etf_holdings.empty:
            if any(" " in col for col in etf_holdings.columns):
                clean_columns = [col.split(" ")[0] for col in etf_holdings.columns]
                etf_holdings.columns = clean_columns
            print(tabulate(etf_holdings, headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No ETF holdings found.")
    except Exception as e:
        print(f"Error fetching ETF holdings: {e}")

async def fetch_fund_holdings(session: aiohttp.ClientSession, symbol):
    """Fetches Fund holdings asynchronously."""
    print("\n--- Fund Holdings (Yahoo) ---")
    try:
        fund_holdings = await fundHoldingsYahoo(session, symbol)
        if not fund_holdings.empty:
            if 'Reporting Date' in fund_holdings.columns:
                fund_holdings = fund_holdings.drop(columns=['Reporting Date'])
            print(tabulate(fund_holdings, headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No Fund holdings found.")
    except Exception as e:
        print(f"Error fetching Fund holdings: {e}")

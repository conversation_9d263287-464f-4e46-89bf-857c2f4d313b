import robin_stocks.robinhood as r
import pandas as pd
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timezone
from tabulate import tabulate
import yfinance as yf
import os
from dotenv import load_dotenv
from common.http_session import get_session
import asyncio
from functools import partial
import aiohttp

# Disable SSL verification to fix certificate issues
session = get_session('src/certs/my_ca_bundle.pem')

# Load environment variables from .env file
load_dotenv()

# Get Robinhood credentials from environment variables
rh_un = os.environ.get('RH_UN')
rh_pw = os.environ.get('RH_PW')

# Check if credentials are available
if not rh_un or not rh_pw:
    print("Warning: Robinhood credentials not found in environment variables. Some features may be limited.")

# Import from sibling modules within the package
from .utils import str_to_num, cache_for_one_hour, format_market_cap

# Import from potentially external utilities/packages relative to src
try:
    from util.scrapper import fundHoldingsYahoo, etfHoldingsETFDBdotCOM, CNNFearGreed, squeezeMetrics
    from stockdata.factors import calculateFactors
except ImportError as e:
    print(f"Warning: Could not import external utilities (scrapper, factors) using direct paths: {e}")
    def fundHoldingsYahoo(session, ticker): return pd.DataFrame()
    def etfHoldingsETFDBdotCOM(session, ticker): return pd.DataFrame()
    def CNNFearGreed(session): return 0
    def squeezeMetrics(session): return pd.DataFrame()
    def calculateFactors(): return 0.0

async def _run_sync_in_executor(func, *args, **kwargs):
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))

async def _fallback_to_yfinance_eod(symbol):
    """
    Fallback function that replicates Robinhood data fetching using yfinance.
    Fetches both historical daily data and today's 5-minute intraday data, just like RH does.

    Replicates the exact RH pattern:
    1. Fetch daily historical data (1 year)
    2. Fetch 5-minute intraday data for today (last 60 days max due to yfinance limit)
    3. Combine them into Robinhood-compatible format
    """
    print(f"Fetching yfinance data (replicating RH pattern) for {symbol}...")
    data = pd.DataFrame()
    data1 = pd.DataFrame()
    df_day = pd.DataFrame()

    # Step 1: Fetch daily historical data (equivalent to RH daily historicals)
    try:
        print("Fetching daily historical data from yfinance...")
        data = await _run_sync_in_executor(yf.download, symbol, period='1y', interval='1d', auto_adjust=False)
        if data.empty:
            print("Warning: yfinance daily historicals returned empty data.")
        else:
            print(f"Retrieved {len(data)} days of historical data")
    except Exception as e:
        print(f"Error fetching yfinance daily historicals: {e}")

    # Step 2: Fetch 5-minute intraday data for recent period (equivalent to RH 5-minute data)
    try:
        print("Fetching 5-minute intraday data from yfinance...")
        # yfinance limitation: intraday data cannot extend last 60 days, so use shorter period
        data1 = await _run_sync_in_executor(yf.download, symbol, period='5d', interval='5m', auto_adjust=False)
        if data1.empty:
            print("Warning: yfinance 5-min intraday data returned empty.")
        else:
            print(f"Retrieved {len(data1)} 5-minute intervals of intraday data")
    except Exception as e:
        print(f"Error fetching yfinance 5-min intraday data: {e}")

    # Step 3: Process 5-minute data to get today's OHLCV (replicating RH logic)
    try:
        if not data1.empty:
            # Reset index to get Datetime column
            data1_reset = data1.reset_index()

            # Handle different possible index names from yfinance
            datetime_col = None
            for col in ['Datetime', 'Date', 'index']:
                if col in data1_reset.columns:
                    datetime_col = col
                    break

            if datetime_col is None:
                print("Warning: Could not find datetime column in intraday data")
                df_day = pd.DataFrame()
            else:
                data1_reset['begins_at'] = pd.to_datetime(data1_reset[datetime_col])
                data1_reset['date'] = data1_reset['begins_at'].dt.date

                # Handle MultiIndex columns from yfinance
                # yfinance returns columns like ('Close', 'AFRM'), ('Open', 'AFRM'), etc.
                ticker_symbol = symbol  # Use the symbol we're fetching

                column_mapping = {
                    'Open': 'open_price',
                    'Close': 'close_price',
                    'High': 'high_price',
                    'Low': 'low_price',
                    'Volume': 'volume'
                }

                for old_col, new_col in column_mapping.items():
                    # Try different ways to access the column
                    col_data = None

                    # Method 1: Direct column access (for simple columns)
                    if old_col in data1_reset.columns:
                        col_data = data1_reset[old_col]
                    # Method 2: MultiIndex access (for yfinance MultiIndex columns)
                    elif (old_col, ticker_symbol) in data1_reset.columns:
                        col_data = data1_reset[(old_col, ticker_symbol)]
                    # Method 3: Try to find any column containing the price type
                    else:
                        for col in data1_reset.columns:
                            if isinstance(col, tuple) and col[0] == old_col:
                                col_data = data1_reset[col]
                                break

                    if col_data is not None:
                        # Debug: Check what type of object we got
                        print(f"Debug: {old_col} -> {new_col}, type: {type(col_data)}")

                        # Handle different data types
                        if isinstance(col_data, pd.DataFrame):
                            # If it's a DataFrame, try to get the first (and likely only) column
                            if len(col_data.columns) == 1:
                                col_data = col_data.iloc[:, 0]
                            else:
                                print(f"Warning: {old_col} returned DataFrame with multiple columns: {col_data.columns}")
                                col_data = col_data.iloc[:, 0]  # Take first column

                        # Now convert to numeric
                        try:
                            data1_reset[new_col] = pd.to_numeric(col_data, errors='coerce')
                        except Exception as e:
                            print(f"Error converting {old_col} to numeric: {e}")
                            print(f"col_data type: {type(col_data)}, shape: {getattr(col_data, 'shape', 'N/A')}")
                            data1_reset[new_col] = pd.NA
                    else:
                        print(f"Warning: Could not find column {old_col} in intraday data")
                        data1_reset[new_col] = pd.NA

                # Group by date to create daily OHLCV from 5-minute data (replicating RH aggregation)
                if all(col in data1_reset.columns for col in ['begins_at', 'open_price', 'close_price', 'high_price', 'low_price', 'volume']):
                    df_day = data1_reset.groupby('date').agg(
                        begins_at=('begins_at', 'last'),
                        open_price=('open_price', 'first'),
                        close_price=('close_price', 'last'),
                        high_price=('high_price', 'max'),
                        low_price=('low_price', 'min'),
                        volume=('volume', 'sum')
                    )
                    print(f"Aggregated intraday data into {len(df_day)} daily records")
                else:
                    print("Warning: Missing required columns for intraday aggregation")
                    df_day = pd.DataFrame()

    except Exception as e:
        print(f"Error processing 5-minute intraday data: {e}")
        import traceback
        traceback.print_exc()
        df_day = pd.DataFrame()

    # Step 4: Combine historical and today's data (replicating RH combination logic)
    try:
        if not data.empty:
            # Convert historical data to RH format
            data_reset = data.reset_index()

            # Handle different possible index names from yfinance
            date_col = None
            for col in ['Date', 'Datetime', 'index']:
                if col in data_reset.columns:
                    date_col = col
                    break

            if date_col is None:
                print("Warning: Could not find date column in historical data")
                return pd.DataFrame()

            data_reset['begins_at'] = pd.to_datetime(data_reset[date_col])

            # Rename columns to RH format
            column_mapping = {
                'Open': 'open_price',
                'Close': 'close_price',
                'High': 'high_price',
                'Low': 'low_price',
                'Volume': 'volume'
            }

            # Handle MultiIndex columns from yfinance (same logic as intraday data)
            ticker_symbol = symbol

            for old_col, new_col in column_mapping.items():
                # Try different ways to access the column
                col_data = None

                # Method 1: Direct column access (for simple columns)
                if old_col in data_reset.columns:
                    col_data = data_reset[old_col]
                # Method 2: MultiIndex access (for yfinance MultiIndex columns)
                elif (old_col, ticker_symbol) in data_reset.columns:
                    col_data = data_reset[(old_col, ticker_symbol)]
                # Method 3: Try to find any column containing the price type
                else:
                    for col in data_reset.columns:
                        if isinstance(col, tuple) and col[0] == old_col:
                            col_data = data_reset[col]
                            break

                if col_data is not None:
                    # Handle different data types (same logic as intraday)
                    if isinstance(col_data, pd.DataFrame):
                        # If it's a DataFrame, try to get the first (and likely only) column
                        if len(col_data.columns) == 1:
                            col_data = col_data.iloc[:, 0]
                        else:
                            col_data = col_data.iloc[:, 0]  # Take first column

                    # Now convert to numeric
                    try:
                        data_reset[new_col] = pd.to_numeric(col_data, errors='coerce')
                    except Exception as e:
                        print(f"Error converting {old_col} to numeric in historical data: {e}")
                        data_reset[new_col] = pd.NA
                else:
                    print(f"Warning: Could not find column {old_col} in historical data")
                    data_reset[new_col] = pd.NA

            # Convert numeric columns
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in data_reset.columns:
                    data_reset[col] = pd.to_numeric(data_reset[col], errors='coerce')

            # Combine with intraday data if available (replicating RH merge logic)
            if not df_day.empty:
                latest_date_in_df_day = df_day.index[-1]
                df_day_reset = df_day.reset_index()
                df_day_reset = df_day_reset.reindex(columns=data_reset.columns, fill_value=pd.NA)
                # Remove overlapping date from historical data
                data_reset = data_reset[data_reset['begins_at'].dt.date != latest_date_in_df_day]
                # Combine historical + today's intraday
                data_reset = pd.concat([data_reset, df_day_reset], ignore_index=True)

            # Set index and sort (replicating RH final format)
            data_reset.set_index('begins_at', inplace=True)
            data_reset.sort_index(inplace=True)

            # Ensure proper data types
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in data_reset.columns:
                    data_reset[col] = data_reset[col].astype(float)

            print(f"Successfully combined data: {len(data_reset)} total records")
            print(f"Latest data point: Index={data_reset.index[-1]}, Close={data_reset['close_price'].iloc[-1]:.2f}, Volume={data_reset['volume'].iloc[-1]:,.0f}")
            return data_reset

        elif not df_day.empty:
            print("Warning: Historical data is empty, using only today's intraday data.")
            df_day.set_index('begins_at', inplace=True)
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in df_day.columns:
                    df_day[col] = df_day[col].astype(float)
            return df_day

        else:
            print("Error: Both historical and intraday data fetching failed.")
            return pd.DataFrame()

    except Exception as e:
        print(f"Error combining yfinance data: {e}")
        return pd.DataFrame()

async def login_robinhood():
    """Logs into Robinhood asynchronously."""
    if rh_un and rh_pw:
        try:
            await _run_sync_in_executor(r.authentication.login, username=rh_un, password=rh_pw)
            print("Robinhood login successful.")
            return True
        except Exception as e:
            print(f"Robinhood login failed: {e}")
            return False
    else:
        print("Robinhood credentials not found. Skipping login.")
        return False

# Apply cache decorator
@cache_for_one_hour
async def get_cached_factors(session: aiohttp.ClientSession):
    """Calculates and caches factors asynchronously."""
    try:
        print("Calculating risk factors...")
        factors = await calculateFactors(session)
        print(f"Raw factors value: {factors}")
        return round(factors, 2)
    except Exception as e:
        import traceback
        print(f"Error calculating factors: {e}")
        traceback.print_exc()
        return 0.0


@cache_for_one_hour
async def fetch_stock_data(symbol):
    """Fetches historical stock data, combining Robinhood and web scraping asynchronously."""
    print(f"Fetching data for {symbol}...")
    data = pd.DataFrame()
    data1 = pd.DataFrame()
    df_day = pd.DataFrame()

    # Fetch daily data from Robinhood
    try:
        data = await _run_sync_in_executor(r.stocks.get_stock_historicals, symbol, interval='day', span='year')
        data = pd.DataFrame(data)
        if data.empty:
            print("Warning: Robinhood daily historicals returned empty data.")
    except Exception as e:
        print(f"Error fetching Robinhood daily historicals: {e}")

    # Fetch 5-minute data for today from Robinhood
    try:
        data1 = await _run_sync_in_executor(r.stocks.get_stock_historicals, symbol, interval='5minute', span='day', bounds="extended")
        data1 = pd.DataFrame(data1)
        if data1.empty:
            print("Warning: Robinhood 5-min historicals returned empty data.")
    except Exception as e:
        print(f"Error fetching Robinhood 5-min historicals: {e}")

    # Process 5-minute data to get today's OHLCV
    try:
        if not data1.empty:
            data1['begins_at'] = pd.to_datetime(data1['begins_at'])
            data1['date'] = data1['begins_at'].dt.date

            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                data1[col] = pd.to_numeric(data1[col], errors='coerce')

            df_day = data1.groupby('date').agg(
                begins_at=('begins_at', 'last'),
                open_price=('open_price', 'first'),
                close_price=('close_price', 'last'),
                high_price=('high_price', 'max'),
                low_price=('low_price', 'min'),
                volume=('volume', 'sum')
            )

            # Attempt to scrape current volume from Robinhood website asynchronously
            try:
                url = f'https://robinhood.com/stocks/{symbol}/'
                async with aiohttp.ClientSession() as session_aio:
                    async with session_aio.get(url, timeout=10) as response_aio:
                        response_aio.raise_for_status()
                        content = await response_aio.text()
                        soup = BeautifulSoup(content, 'html.parser')
                        volume_span = soup.find('span', text='Volume')
                        if volume_span:
                            volume_data = volume_span.find_next_sibling('span').text.strip()
                            if not df_day.empty:
                                df_day.loc[df_day.index[-1], 'volume'] = str_to_num(volume_data)
            except Exception as e:
                print(f"Error scraping volume from Robinhood: {e}")

    except Exception as e:
        print(f"Error processing 5-minute data: {e}")
        df_day = pd.DataFrame()

    # Check if we have any Robinhood data at all, if not, use yfinance fallback immediately
    if data.empty and df_day.empty:
        print("No Robinhood data available. Using yfinance fallback (with 5-min intraday)...")
        return await _fallback_to_yfinance_eod(symbol)

    # Combine historical and today's data
    try:
        if not data.empty:
            data['begins_at'] = pd.to_datetime(data['begins_at'])
            required_cols = ['begins_at', 'open_price', 'close_price', 'high_price', 'low_price', 'volume']
            for col in required_cols:
                if col not in data.columns:
                    data[col] = pd.NA
                if col != 'begins_at':
                    data[col] = pd.to_numeric(data[col], errors='coerce')

            if not df_day.empty:
                latest_date_in_df_day = df_day.index[-1]
                df_day.reset_index(inplace=True)
                df_day = df_day.reindex(columns=data.columns)
                data = data[data['begins_at'].dt.date != latest_date_in_df_day]
                data = pd.concat([data, df_day], ignore_index=True)

            data.set_index('begins_at', inplace=True)
            data.sort_index(inplace=True)

            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in data.columns:
                    data[col] = data[col].astype(float)

            print(f"Latest data point: Index={data.index[-1]}, Close={data['close_price'].iloc[-1]:.2f}, Volume={data['volume'].iloc[-1]:,.0f}")
            return data

        elif not df_day.empty:
            print("Warning: Historical data is empty, using only today's data.")
            df_day.set_index('begins_at', inplace=True)
            for col in ['open_price', 'close_price', 'high_price', 'low_price', 'volume']:
                if col in df_day.columns:
                    df_day[col] = df_day[col].astype(float)
            return df_day

        else:
            print("Error: Both historical and today's data fetching failed.")
            print("Attempting to use yfinance fallback (with 5-min intraday)...")
            return await _fallback_to_yfinance_eod(symbol)

    except Exception as e:
        print(f"Error combining or processing data: {e}")
        print("Attempting to use yfinance fallback (with 5-min intraday)...")
        return await _fallback_to_yfinance_eod(symbol)


async def fetch_basic_info_and_news(symbol):
    """Fetches basic company info and news using yfinance asynchronously."""
    print("\n--- Ticker Information & News ---")
    try:
        tickerData = await _run_sync_in_executor(yf.Ticker, symbol, session=session)
        info = await _run_sync_in_executor(lambda: tickerData.info)

        description = info.get('longBusinessSummary', 'N/A')
        sector = info.get('sector', 'N/A')
        industry = info.get('industry', 'N/A')
        market_cap_raw = info.get('marketCap', 0)
        market_cap = format_market_cap(market_cap_raw)

        earnings_dates = await _run_sync_in_executor(lambda: tickerData.calendar) if hasattr(tickerData, 'calendar') else None
        earnings_date = 'N/A'
        if earnings_dates is not None and isinstance(earnings_dates, pd.DataFrame) and not earnings_dates.empty:
            try:
                raw_date = earnings_dates.iloc[0, earnings_dates.columns.get_loc('Earnings Date')]
                if pd.notna(raw_date):
                    earnings_date = pd.to_datetime(raw_date).strftime('%Y-%m-%d')
            except Exception as e:
                print(f"Could not parse earnings date: {e}")
                earnings_date = 'N/A'

        print(f"Description: {description[:150]}...")
        print(f"Sector: {sector}")
        print(f"Industry: {industry}")
        print(f"Earnings Date: {earnings_date}")
        print(f"Market Cap: {market_cap}")

        print("\n--- Recent News (Yahoo) ---")
        try:
            news = await _run_sync_in_executor(tickerData.get_news, count=5, tab="news")
            if news and isinstance(news, list) and len(news) > 0:
                for item in news[:5]:
                    title = item.get('title', 'N/A')
                    if 'content' in item and 'title' in item['content']:
                        title = item['content']['title']

                    link = '#'
                    if 'content' in item and 'clickThroughUrl' in item['content']:
                        if 'url' in item['content']['clickThroughUrl']:
                            link = item['content']['clickThroughUrl']['url']
                    elif 'link' in item:
                        link = item['link']

                    print(f"- {title} ({link})")
            else:
                print("No recent news found via yfinance.")
        except Exception as news_error:
            print(f"Error fetching news: {news_error}")
            print("No recent news found via yfinance.")

    except Exception as e:
        print(f"Error fetching basic info/news from yfinance: {e}")


async def print_catalyst_news(symbol):
    """Fetches and prints catalyst news from Robinhood asynchronously."""
    print("\n--- Catalyst News (Robinhood) ---")
    try:
        today_utc = datetime.now(timezone.utc).date().isoformat()
        today_local = datetime.now().date().isoformat()

        all_news = await _run_sync_in_executor(r.stocks.get_news, symbol)

        if not all_news:
            print("No news found from Robinhood.")
            return

        todays_news = [news for news in all_news if
                      news.get('published_at', '').startswith(today_local) or
                      news.get('published_at', '').startswith(today_utc)]

        if todays_news:
            for news in todays_news[:5]:
                title = news.get('title', 'N/A')
                print(f"- {title}")
        else:
            print("No news published today found from Robinhood.")
            print("\n--- Recent Headlines ---")
            for news in all_news[:3]:
                title = news.get('title', 'N/A')
                print(f"- {title}")

    except Exception as e:
        print(f"Error fetching Robinhood news: {e}")
        import traceback
        traceback.print_exc()


def fetch_and_print_highs_lows(data):
    """Calculates and prints historical highs/lows relative to the current price."""
    print("\n--- Highs & Lows ---")
    if data.empty or 'close_price' not in data.columns:
        print("Insufficient data for highs/lows calculation.")
        return

    try:
        # Ensure index is datetime
        if not isinstance(data.index, pd.DatetimeIndex):
             data.index = pd.to_datetime(data.index)

        # Calculate the highs and lows for each timeframe
        timeframes = {'1w': '7D', '1m': '30D', '3m': '90D', '6m': '180D', '12m': '365D'} # Use pandas offset strings
        results = {}
        current_price = data['close_price'].iloc[-1]

        for tf, duration in timeframes.items():
            # Rolling window calculation - ensure enough data points
            if len(data) > 1:
                 # Shift(1) to get the high/low *before* the current day
                 high_prices = data['close_price'].rolling(window=duration, min_periods=1).max().shift(1)
                 low_prices = data['close_price'].rolling(window=duration, min_periods=1).min().shift(1)
                 prev_high = high_prices.iloc[-1] if not high_prices.empty else pd.NA
                 prev_low = low_prices.iloc[-1] if not low_prices.empty else pd.NA
            else:
                 prev_high = pd.NA
                 prev_low = pd.NA


            results[tf] = {
                'high': prev_high if pd.notna(prev_high) else 'N/A',
                'low': prev_low if pd.notna(prev_low) else 'N/A',
                # Compare current price to the *previous* period's high/low
                'is_high': current_price >= prev_high if pd.notna(prev_high) else False,
                'is_low': current_price <= prev_low if pd.notna(prev_low) else False
            }

        # Create a DataFrame from the dictionary
        df = pd.DataFrame(results).T

        # Rename columns
        df.columns = ["Prev High", "Prev Low", ">= Prev High", "<= Prev Low"]

        # Format numbers and boolean strings
        df["Prev High"] = df["Prev High"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
        df["Prev Low"] = df["Prev Low"].apply(lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x)
        df.replace({True: 'True', False: 'False'}, inplace=True)


        # Print the DataFrame
        print(tabulate(df, headers='keys', tablefmt='fancy_grid'))

    except Exception as e:
        print(f"Error calculating highs/lows: {e}")


async def fetch_etf_holdings(session: aiohttp.ClientSession, symbol):
    """Fetches ETF holdings asynchronously."""
    print("\n--- ETF Holdings (ETFDB.com) ---")
    try:
        etf_holdings = await etfHoldingsETFDBdotCOM(session, symbol)
        if not etf_holdings.empty:
            if any(" " in col for col in etf_holdings.columns):
                clean_columns = [col.split(" ")[0] for col in etf_holdings.columns]
                etf_holdings.columns = clean_columns
            print(tabulate(etf_holdings, headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No ETF holdings found.")
    except Exception as e:
        print(f"Error fetching ETF holdings: {e}")

async def fetch_fund_holdings(session: aiohttp.ClientSession, symbol):
    """Fetches Fund holdings asynchronously."""
    print("\n--- Fund Holdings (Yahoo) ---")
    try:
        fund_holdings = await fundHoldingsYahoo(session, symbol)
        if not fund_holdings.empty:
            if 'Reporting Date' in fund_holdings.columns:
                fund_holdings = fund_holdings.drop(columns=['Reporting Date'])
            print(tabulate(fund_holdings, headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No Fund holdings found.")
    except Exception as e:
        print(f"Error fetching Fund holdings: {e}")

#!/usr/bin/env python3
"""
Test script to verify yfinance fallback functionality for ticker analyzer.
This script tests the enhanced getStockDataV3 function to ensure it works
properly as a fallback when Robinhood data is unavailable.
"""

import asyncio
import aiohttp
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from stockdata.data_source import getStockDataV3
from ticker_analyzer.indicators import calculate_atr, calculate_rvol, calculate_mas, calculate_rsi

async def test_yfinance_fallback(ticker='AFRM'):
    """Test the yfinance fallback functionality."""
    print(f"Testing yfinance fallback for {ticker}...")
    
    async with aiohttp.ClientSession() as session:
        # Test getStockDataV3 directly
        print("\n=== Testing getStockDataV3 ===")
        stock_data = await getStockDataV3(session, ticker)
        
        if stock_data.empty:
            print("❌ Failed to retrieve data using getStockDataV3")
            return False
        
        print(f"✅ Successfully retrieved {len(stock_data)} days of data")
        print(f"Columns: {list(stock_data.columns)}")
        print(f"Latest data point:")
        print(f"  Date: {stock_data.index[-1]}")
        print(f"  Close: ${stock_data['close_price'].iloc[-1]:.2f}")
        print(f"  Volume: {stock_data['volume'].iloc[-1]:,.0f}")
        
        # Test technical indicators
        print("\n=== Testing Technical Indicators ===")
        
        # Test ATR calculation
        try:
            stock_data = calculate_atr(stock_data)
            latest_atr = stock_data['atr'].iloc[-1]
            print(f"✅ ATR calculated: {latest_atr:.2f}")
        except Exception as e:
            print(f"❌ ATR calculation failed: {e}")
            return False
        
        # Test RVOL calculation
        try:
            stock_data = calculate_rvol(stock_data)
            latest_rvol = stock_data['rvol'].iloc[-1]
            print(f"✅ RVOL calculated: {latest_rvol:.2f}")
        except Exception as e:
            print(f"❌ RVOL calculation failed: {e}")
            return False
        
        # Test Moving Averages
        try:
            stock_data = calculate_mas(stock_data)
            latest_sma50 = stock_data['sma50'].iloc[-1]
            print(f"✅ SMA50 calculated: ${latest_sma50:.2f}")
        except Exception as e:
            print(f"❌ MA calculation failed: {e}")
            return False
        
        # Test RSI calculation
        try:
            rsi_series = calculate_rsi(stock_data)
            latest_rsi = rsi_series.iloc[-1]
            print(f"✅ RSI calculated: {latest_rsi:.2f}")
        except Exception as e:
            print(f"❌ RSI calculation failed: {e}")
            return False
        
        print("\n=== Data Format Verification ===")
        required_columns = ['open_price', 'close_price', 'high_price', 'low_price', 'volume']
        missing_columns = [col for col in required_columns if col not in stock_data.columns]
        
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
        
        # Check data types
        for col in required_columns:
            if not stock_data[col].dtype.kind in 'biufc':  # numeric types
                print(f"❌ Column {col} is not numeric: {stock_data[col].dtype}")
                return False
        
        print("✅ All columns have correct numeric types")
        
        # Check for sufficient data for position sizing
        if len(stock_data) < 50:
            print(f"⚠️  Warning: Only {len(stock_data)} days of data (recommended: 50+)")
        else:
            print(f"✅ Sufficient data: {len(stock_data)} days")
        
        print(f"\n=== Summary ===")
        print(f"✅ yfinance fallback is working correctly for {ticker}")
        print(f"✅ All technical indicators can be calculated")
        print(f"✅ Data format is compatible with position sizing")
        
        return True

async def test_data_fetcher_fallback(ticker='AFRM'):
    """Test the data fetcher fallback functionality."""
    print(f"\n=== Testing Data Fetcher Fallback for {ticker} ===")
    
    # Import the fetch_stock_data function
    from ticker_analyzer.data_fetcher import fetch_stock_data
    
    try:
        stock_data = await fetch_stock_data(ticker)
        
        if stock_data.empty:
            print("❌ Data fetcher returned empty DataFrame")
            return False
        
        print(f"✅ Data fetcher returned {len(stock_data)} days of data")
        print(f"Latest close: ${stock_data['close_price'].iloc[-1]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data fetcher failed: {e}")
        return False

if __name__ == "__main__":
    ticker = sys.argv[1] if len(sys.argv) > 1 else 'AFRM'
    
    async def main():
        print(f"Testing yfinance fallback functionality with {ticker}")
        print("=" * 60)
        
        # Test getStockDataV3 directly
        success1 = await test_yfinance_fallback(ticker)
        
        # Test data fetcher fallback
        success2 = await test_data_fetcher_fallback(ticker)
        
        if success1 and success2:
            print(f"\n🎉 All tests passed! yfinance fallback is working correctly.")
        else:
            print(f"\n❌ Some tests failed. Check the output above.")
    
    asyncio.run(main())
